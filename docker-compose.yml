version: "2.1"
services:
  flaresolverr:
    # 官方镜像: ghcr.io/flaresolverr/flaresolverr:latest
    # DockerHub镜像: flaresolverr/flaresolverr:latest
    # 阿里云镜像: registry.cn-hangzhou.aliyuncs.com/flaresolverr/flaresolverr:latest
    # 腾讯云镜像: ccr.ccs.tencentyun.com/flaresolverr/flaresolverr:latest
    image: ${FLARESOLVERR_IMAGE:-ghcr.io/flaresolverr/flaresolverr:latest}
    container_name: flaresolverr
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_HTML=${LOG_HTML:-false}
      - CAPTCHA_SOLVER=${CAPTCHA_SOLVER:-none}
      - TZ=Asia/Shanghai
    ports:
      - "${PORT:-8191}:8191"
    restart: unless-stopped
