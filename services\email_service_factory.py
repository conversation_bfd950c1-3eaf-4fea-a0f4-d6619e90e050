"""
邮箱服务工厂
"""
from typing import Dict, List, Optional
from services.email_service_base import EmailServiceBase

from services.linshiguge_flaresolverr_service import LinshigugeFlareSolverrService
from config.settings import settings
from utils.logger import app_logger


class EmailServiceFactory:
    """邮箱服务工厂"""
    
    _services: Dict[str, EmailServiceBase] = {}
    
    @classmethod
    def register_service(cls, name: str, service_class: type):
        """注册邮箱服务"""
        if name in settings.EMAIL_SERVICES:
            config = settings.EMAIL_SERVICES[name]
            service = service_class(config)
            cls._services[name] = service
            app_logger.info(f"注册邮箱服务: {name}")
    
    @classmethod
    def get_service(cls, name: str) -> Optional[EmailServiceBase]:
        """获取邮箱服务"""
        return cls._services.get(name)
    
    @classmethod
    def get_available_services(cls) -> List[EmailServiceBase]:
        """获取可用的邮箱服务"""
        available = []
        for service in cls._services.values():
            if service.enabled:
                available.append(service)
        
        # 按优先级排序
        available.sort(key=lambda x: x.priority)
        return available
    
    @classmethod
    def get_best_service(cls) -> Optional[EmailServiceBase]:
        """获取最佳邮箱服务"""
        available = cls.get_available_services()
        return available[0] if available else None
    
    @classmethod
    def initialize_services(cls):
        """初始化所有邮箱服务"""
        # 使用FlareSolverr服务
        cls.register_service("linshiguge", LinshigugeFlareSolverrService)
        app_logger.info("使用FlareSolverr版本的邮箱服务")

        # 可以在这里注册更多服务
        # cls.register_service("temp_mail", TempMailService)
        # cls.register_service("gmail_alias", GmailAliasService)

        app_logger.info(f"已初始化 {len(cls._services)} 个邮箱服务")


# 初始化服务
EmailServiceFactory.initialize_services()
