# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from threading import Lock
from typing import List, Optional, Set, Dict, Union, Tuple

from .driver import BrowserDriver, Driver
from .._configs.chromium_options import ChromiumOptions
from .._configs.session_options import SessionOptions
from .._functions.cookies import CookiesList
from .._pages.chromium_base import Timeout
from .._pages.tabs import ChromiumTab, MixTab
from .._units.downloader import DownloadManager
from .._units.setter import BrowserSetter
from .._units.waiter import BrowserWaiter


class Chromium(object):
    id: str = ...
    address: str = ...
    version: str = ...
    retry_times: int = ...
    retry_interval: float = ...
    is_headless: bool = ...

    _BROWSERS: dict = ...
    _chromium_options: ChromiumOptions = ...
    _session_options: SessionOptions = ...
    _driver: BrowserDriver = ...
    _frames: dict = ...
    _drivers: Dict[str, Driver] = ...
    _all_drivers: Dict[str, Set[Driver]] = ...
    _process_id: Optional[int] = ...
    _dl_mgr: DownloadManager = ...
    _lock: Lock = ...

    _set: Optional[BrowserSetter] = ...
    _wait: Optional[BrowserWaiter] = ...
    _timeouts: Timeout = ...
    _load_mode: str = ...
    _download_path: str = ...
    _is_exists: bool = ...

    def __new__(cls,
                addr_or_opts: Union[str, int, ChromiumOptions] = None,
                session_options: Optional[SessionOptions] = None): ...

    def __init__(self, addr_or_opts: Union[str, int, ChromiumOptions] = None,
                 session_options: Optional[SessionOptions] = None): ...

    def _get_driver(self, tab_id: str, owner=None) -> Driver: ...

    def _run_cdp(self, cmd, **cmd_args) -> dict: ...

    @property
    def user_data_path(self) -> str: ...

    @property
    def process_id(self) -> Optional[int]: ...

    @property
    def timeout(self) -> float: ...

    @property
    def timeouts(self) -> Timeout: ...

    @property
    def load_mode(self) -> str: ...

    @property
    def download_path(self) -> str: ...

    @property
    def set(self) -> BrowserSetter: ...

    @property
    def wait(self) -> BrowserWaiter: ...

    @property
    def tabs_count(self) -> int: ...

    @property
    def tab_ids(self) -> List[str]: ...

    @property
    def latest_tab(self) -> Union[ChromiumTab, str]: ...

    def cookies(self, all_info: bool = False) -> CookiesList: ...

    def close_tabs(self,
                   tabs_or_ids: Union[str, ChromiumTab, List[Union[str, ChromiumTab]],
                   Tuple[Union[str, ChromiumTab]]] = None,
                   others: bool = False) -> None: ...

    def get_tab(self,
                id_or_num: Union[str, int] = None,
                title: str = None,
                url: str = None,
                tab_type: str = 'page',
                as_id: bool = False) -> Union[ChromiumTab, str]: ...

    def get_tabs(self,
                 title: str = None,
                 url: str = None,
                 tab_type: str = 'page',
                 as_id: bool = False) -> List[ChromiumTab, str]: ...

    def get_mix_tab(self,
                    id_or_num: Union[str, int] = None,
                    title: str = None,
                    url: str = None,
                    tab_type: str = 'page') -> Union[MixTab, str]: ...

    def get_mix_tabs(self,
                     title: str = None,
                     url: str = None,
                     tab_type: str = 'page') -> List[MixTab, str]: ...

    def _get_tab(self,
                 id_or_num: Union[str, int] = None,
                 title: str = None,
                 url: str = None,
                 tab_type: str = 'page',
                 mix: bool = False,
                 as_id: bool = False) -> Union[ChromiumTab, str]: ...

    def _get_tabs(self,
                  title: str = None,
                  url: str = None,
                  tab_type: str = 'page',
                  mix: bool = False,
                  as_id: bool = False) -> List[ChromiumTab, str]: ...

    def activate_tab(self, id_ind_tab: Union[int, str, ChromiumTab]) -> None: ...

    def _new_tab(self,
                 obj,
                 url: str = None,
                 new_window: bool = False,
                 background: bool = False,
                 new_context: bool = False) -> Union[ChromiumTab, MixTab]: ...

    def new_tab(self,
                url: str = None,
                new_window: bool = False,
                background: bool = False,
                new_context: bool = False) -> ChromiumTab: ...

    def new_mix_tab(self,
                    url: str = None,
                    new_window: bool = False,
                    background: bool = False,
                    new_context: bool = False) -> MixTab: ...

    def reconnect(self) -> None: ...

    def _onTargetCreated(self, **kwargs) -> None: ...

    def _onTargetDestroyed(self, **kwargs) -> None: ...

    def quit(self, timeout: float = 5, force: bool = False) -> None: ...

    def _on_disconnect(self) -> None: ...
