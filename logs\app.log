2025-07-30 11:38:20 | INFO     | services.email_service_factory:register_service:23 - 注册邮箱服务: linshiguge
2025-07-30 11:38:20 | INFO     | services.email_service_factory:initialize_services:58 - 已初始化 1 个邮箱服务
2025-07-30 11:38:35 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 11:38:35 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 1 个账户
2025-07-30 11:38:38 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:38:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: testy5jv8k1b
2025-07-30 11:38:38 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 1 个
2025-07-30 11:38:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 752194ad-ae2f-4035-a97b-25c72ca39d26
2025-07-30 11:38:38 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 1 个注册任务
2025-07-30 11:38:38 | ERROR    | core.http_client:start:49 - HTTP客户端启动失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:38:38 | ERROR    | core.registration_controller:start_batch_registration:65 - 批量注册失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:38:38 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 11:41:06 | INFO     | services.email_service_factory:register_service:23 - 注册邮箱服务: linshiguge
2025-07-30 11:41:06 | INFO     | services.email_service_factory:initialize_services:58 - 已初始化 1 个邮箱服务
2025-07-30 11:41:24 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 11:41:24 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 1 个账户
2025-07-30 11:41:25 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:41:25 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: testcy7fzwoa
2025-07-30 11:41:25 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 1 个
2025-07-30 11:41:25 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 274b5c3a-22c0-48d9-ae71-3d4f79a408b5
2025-07-30 11:41:25 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 1 个注册任务
2025-07-30 11:41:25 | ERROR    | core.http_client:start:49 - HTTP客户端启动失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:41:25 | ERROR    | core.registration_controller:start_batch_registration:65 - 批量注册失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:41:26 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 11:41:47 | INFO     | services.email_service_factory:register_service:23 - 注册邮箱服务: linshiguge
2025-07-30 11:41:47 | INFO     | services.email_service_factory:initialize_services:58 - 已初始化 1 个邮箱服务
2025-07-30 11:41:59 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 11:41:59 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 1 个账户
2025-07-30 11:42:00 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:42:00 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: test2lpapvrq
2025-07-30 11:42:00 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 1 个
2025-07-30 11:42:00 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 062d0f0f-862b-4433-84aa-8e31ca60b5f4
2025-07-30 11:42:00 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 1 个注册任务
2025-07-30 11:42:00 | INFO     | core.http_client:start:51 - HTTP客户端启动成功
2025-07-30 11:42:00 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: test2lpapvrq
2025-07-30 11:42:00 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge
2025-07-30 11:42:00 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:42:00 | INFO     | services.linshiguge_service:create_email:26 - 开始创建linshiguge邮箱
2025-07-30 11:43:29 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("生成"): Timeout 10000ms exceeded.
2025-07-30 11:43:39 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("获取"): Timeout 10000ms exceeded.
2025-07-30 11:43:49 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("新建"): Timeout 10000ms exceeded.
2025-07-30 11:43:59 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 .generate-btn: Timeout 10000ms exceeded.
2025-07-30 11:44:09 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 #generate: Timeout 10000ms exceeded.
2025-07-30 11:49:10 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:22 - 浏览器管理器启动成功
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:27 - 创建邮箱服务: linshiguge
2025-07-30 11:49:10 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:31 - 浏览器上下文创建成功
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:34 - 开始测试创建邮箱...
2025-07-30 11:49:10 | INFO     | services.linshiguge_service:create_email:26 - 开始创建linshiguge邮箱
2025-07-30 11:49:12 | INFO     | services.linshiguge_service:create_email:32 - 等待Cloudflare验证...
2025-07-30 11:49:12 | INFO     | services.linshiguge_service:handle_cloudflare_challenge:365 - Cloudflare验证已通过，到达目标页面
2025-07-30 11:50:30 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("更换gmail"): Timeout 10000ms exceeded.
2025-07-30 11:50:40 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("新邮箱"): Timeout 10000ms exceeded.
2025-07-30 11:50:50 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("更换"): Timeout 10000ms exceeded.
2025-07-30 11:51:01 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 .generate-btn: Timeout 10000ms exceeded.
2025-07-30 11:51:11 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 #generate: Timeout 10000ms exceeded.
2025-07-30 11:51:21 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 [onclick*="generate"]: Timeout 10000ms exceeded.
2025-07-30 11:51:21 | ERROR    | services.linshiguge_service:create_email:67 - 无法获取邮箱地址
2025-07-30 11:51:21 | ERROR    | __main__:test_email_service:43 - ❌ 邮箱创建失败
2025-07-30 11:51:21 | INFO     | core.browser_manager:close_context:172 - 浏览器上下文已关闭
2025-07-30 11:51:21 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 11:51:21 | INFO     | __main__:test_email_service:55 - 浏览器管理器已关闭
2025-07-30 11:53:18 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:22 - 浏览器管理器启动成功
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:27 - 创建邮箱服务: linshiguge
2025-07-30 11:53:18 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:31 - 浏览器上下文创建成功
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:34 - 开始测试创建邮箱...
2025-07-30 11:53:19 | INFO     | services.linshiguge_service:create_email:26 - 开始创建linshiguge邮箱
2025-07-30 11:53:21 | INFO     | services.linshiguge_service:create_email:32 - 等待Cloudflare验证...
2025-07-30 11:53:21 | INFO     | services.linshiguge_service:handle_cloudflare_challenge:383 - Cloudflare验证已通过，到达目标页面
2025-07-30 11:54:27 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:24 - 浏览器管理器启动成功
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:29 - 创建邮箱服务: linshiguge
2025-07-30 11:54:27 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:33 - 浏览器上下文创建成功
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:37 - 页面创建成功
2025-07-30 11:54:29 | INFO     | __main__:debug_email_service:43 - 页面加载完成
2025-07-30 11:54:29 | INFO     | services.linshiguge_service:handle_cloudflare_challenge:383 - Cloudflare验证已通过，到达目标页面
2025-07-30 12:34:38 | INFO     | core.browser_manager:close_context:172 - 浏览器上下文已关闭
2025-07-30 12:34:38 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
