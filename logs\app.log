2025-07-30 11:38:20 | INFO     | services.email_service_factory:register_service:23 - 注册邮箱服务: linshiguge
2025-07-30 11:38:20 | INFO     | services.email_service_factory:initialize_services:58 - 已初始化 1 个邮箱服务
2025-07-30 11:38:35 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 11:38:35 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 1 个账户
2025-07-30 11:38:38 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:38:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: testy5jv8k1b
2025-07-30 11:38:38 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 1 个
2025-07-30 11:38:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 752194ad-ae2f-4035-a97b-25c72ca39d26
2025-07-30 11:38:38 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 1 个注册任务
2025-07-30 11:38:38 | ERROR    | core.http_client:start:49 - HTTP客户端启动失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:38:38 | ERROR    | core.registration_controller:start_batch_registration:65 - 批量注册失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:38:38 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 11:41:06 | INFO     | services.email_service_factory:register_service:23 - 注册邮箱服务: linshiguge
2025-07-30 11:41:06 | INFO     | services.email_service_factory:initialize_services:58 - 已初始化 1 个邮箱服务
2025-07-30 11:41:24 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 11:41:24 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 1 个账户
2025-07-30 11:41:25 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:41:25 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: testcy7fzwoa
2025-07-30 11:41:25 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 1 个
2025-07-30 11:41:25 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 274b5c3a-22c0-48d9-ae71-3d4f79a408b5
2025-07-30 11:41:25 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 1 个注册任务
2025-07-30 11:41:25 | ERROR    | core.http_client:start:49 - HTTP客户端启动失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:41:25 | ERROR    | core.registration_controller:start_batch_registration:65 - 批量注册失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:41:26 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 11:41:47 | INFO     | services.email_service_factory:register_service:23 - 注册邮箱服务: linshiguge
2025-07-30 11:41:47 | INFO     | services.email_service_factory:initialize_services:58 - 已初始化 1 个邮箱服务
2025-07-30 11:41:59 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 11:41:59 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 1 个账户
2025-07-30 11:42:00 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:42:00 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: test2lpapvrq
2025-07-30 11:42:00 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 1 个
2025-07-30 11:42:00 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 062d0f0f-862b-4433-84aa-8e31ca60b5f4
2025-07-30 11:42:00 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 1 个注册任务
2025-07-30 11:42:00 | INFO     | core.http_client:start:51 - HTTP客户端启动成功
2025-07-30 11:42:00 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: test2lpapvrq
2025-07-30 11:42:00 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge
2025-07-30 11:42:00 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:42:00 | INFO     | services.linshiguge_service:create_email:26 - 开始创建linshiguge邮箱
2025-07-30 11:43:29 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("生成"): Timeout 10000ms exceeded.
2025-07-30 11:43:39 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("获取"): Timeout 10000ms exceeded.
2025-07-30 11:43:49 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("新建"): Timeout 10000ms exceeded.
2025-07-30 11:43:59 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 .generate-btn: Timeout 10000ms exceeded.
2025-07-30 11:44:09 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 #generate: Timeout 10000ms exceeded.
2025-07-30 11:49:10 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:22 - 浏览器管理器启动成功
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:27 - 创建邮箱服务: linshiguge
2025-07-30 11:49:10 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:31 - 浏览器上下文创建成功
2025-07-30 11:49:10 | INFO     | __main__:test_email_service:34 - 开始测试创建邮箱...
2025-07-30 11:49:10 | INFO     | services.linshiguge_service:create_email:26 - 开始创建linshiguge邮箱
2025-07-30 11:49:12 | INFO     | services.linshiguge_service:create_email:32 - 等待Cloudflare验证...
2025-07-30 11:49:12 | INFO     | services.linshiguge_service:handle_cloudflare_challenge:365 - Cloudflare验证已通过，到达目标页面
2025-07-30 11:50:30 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("更换gmail"): Timeout 10000ms exceeded.
2025-07-30 11:50:40 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("新邮箱"): Timeout 10000ms exceeded.
2025-07-30 11:50:50 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 button:has-text("更换"): Timeout 10000ms exceeded.
2025-07-30 11:51:01 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 .generate-btn: Timeout 10000ms exceeded.
2025-07-30 11:51:11 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 #generate: Timeout 10000ms exceeded.
2025-07-30 11:51:21 | WARNING  | services.email_service_base:safe_click:86 - 点击元素失败 [onclick*="generate"]: Timeout 10000ms exceeded.
2025-07-30 11:51:21 | ERROR    | services.linshiguge_service:create_email:67 - 无法获取邮箱地址
2025-07-30 11:51:21 | ERROR    | __main__:test_email_service:43 - ❌ 邮箱创建失败
2025-07-30 11:51:21 | INFO     | core.browser_manager:close_context:172 - 浏览器上下文已关闭
2025-07-30 11:51:21 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 11:51:21 | INFO     | __main__:test_email_service:55 - 浏览器管理器已关闭
2025-07-30 11:53:18 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:22 - 浏览器管理器启动成功
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:27 - 创建邮箱服务: linshiguge
2025-07-30 11:53:18 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:31 - 浏览器上下文创建成功
2025-07-30 11:53:18 | INFO     | __main__:test_email_service:34 - 开始测试创建邮箱...
2025-07-30 11:53:19 | INFO     | services.linshiguge_service:create_email:26 - 开始创建linshiguge邮箱
2025-07-30 11:53:21 | INFO     | services.linshiguge_service:create_email:32 - 等待Cloudflare验证...
2025-07-30 11:53:21 | INFO     | services.linshiguge_service:handle_cloudflare_challenge:383 - Cloudflare验证已通过，到达目标页面
2025-07-30 11:54:27 | INFO     | core.browser_manager:start:47 - 浏览器启动成功: chromium
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:24 - 浏览器管理器启动成功
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:29 - 创建邮箱服务: linshiguge
2025-07-30 11:54:27 | INFO     | core.browser_manager:create_context:113 - 浏览器上下文创建成功
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:33 - 浏览器上下文创建成功
2025-07-30 11:54:27 | INFO     | __main__:debug_email_service:37 - 页面创建成功
2025-07-30 11:54:29 | INFO     | __main__:debug_email_service:43 - 页面加载完成
2025-07-30 11:54:29 | INFO     | services.linshiguge_service:handle_cloudflare_challenge:383 - Cloudflare验证已通过，到达目标页面
2025-07-30 12:34:38 | INFO     | core.browser_manager:close_context:172 - 浏览器上下文已关闭
2025-07-30 12:34:38 | INFO     | core.browser_manager:close:192 - 浏览器管理器已关闭
2025-07-30 14:37:19 | INFO     | services.email_service_factory:register_service:24 - 注册邮箱服务: linshiguge
2025-07-30 14:37:19 | INFO     | services.email_service_factory:initialize_services:54 - 使用FlareSolverr版本的邮箱服务
2025-07-30 14:37:19 | INFO     | services.email_service_factory:initialize_services:60 - 已初始化 1 个邮箱服务
2025-07-30 14:37:45 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 14:37:45 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 5 个账户
2025-07-30 14:37:45 | INFO     | services.email_service_factory:register_service:24 - 注册邮箱服务: linshiguge
2025-07-30 14:37:45 | INFO     | services.email_service_factory:initialize_services:54 - 使用FlareSolverr版本的邮箱服务
2025-07-30 14:37:45 | INFO     | services.email_service_factory:initialize_services:60 - 已初始化 1 个邮箱服务
2025-07-30 14:37:45 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: user3n6p0m2e
2025-07-30 14:37:45 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: usery8s5qzcd
2025-07-30 14:37:45 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: usermv9zhuvn
2025-07-30 14:37:45 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: user7r8grjse
2025-07-30 14:37:45 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: usertaw94r2v
2025-07-30 14:37:45 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 5 个
2025-07-30 14:37:45 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 4eb1cbc4-1243-4f97-a467-81a86092d182
2025-07-30 14:37:45 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 8545e2c2-025a-4f96-8d5c-80061b5e9aed
2025-07-30 14:37:45 | DEBUG    | core.data_manager:save_task:32 - 保存任务: da89d5cd-ffda-485d-a894-0fa6ca61b2c8
2025-07-30 14:37:45 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 13ebf2ad-164c-4f39-91bc-c944a628ccdb
2025-07-30 14:37:45 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 8fc1d8c0-1ae8-4d49-b352-f49e2e6e780a
2025-07-30 14:37:45 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 5 个注册任务
2025-07-30 14:37:45 | INFO     | core.http_client:start:51 - HTTP客户端启动成功
2025-07-30 14:37:45 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: user3n6p0m2e
2025-07-30 14:37:45 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:37:45 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:37:45 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: usery8s5qzcd
2025-07-30 14:37:45 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:37:45 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:37:45 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: usermv9zhuvn
2025-07-30 14:37:45 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:37:45 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:37:45 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:37:45 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:37:45 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:37:47 | INFO     | core.flaresolverr_client:create_session:48 - FlareSolverr会话创建成功: session_355941.156
2025-07-30 14:37:47 | INFO     | core.flaresolverr_client:solve_challenge:105 - 使用FlareSolverr解决Cloudflare挑战: https://www.linshiguge.com/
2025-07-30 14:37:47 | INFO     | core.flaresolverr_client:create_session:48 - FlareSolverr会话创建成功: session_355941.156
2025-07-30 14:37:47 | INFO     | core.flaresolverr_client:solve_challenge:105 - 使用FlareSolverr解决Cloudflare挑战: https://www.linshiguge.com/
2025-07-30 14:37:47 | INFO     | core.flaresolverr_client:create_session:48 - FlareSolverr会话创建成功: session_355941.156
2025-07-30 14:37:47 | INFO     | core.flaresolverr_client:solve_challenge:105 - 使用FlareSolverr解决Cloudflare挑战: https://www.linshiguge.com/
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:solve_challenge:139 - FlareSolverr请求失败: HTTP 500
2025-07-30 14:39:08 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:solve_challenge:139 - FlareSolverr请求失败: HTTP 500
2025-07-30 14:39:08 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:solve_challenge:139 - FlareSolverr请求失败: HTTP 500
2025-07-30 14:39:08 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:destroy_session:81 - 销毁FlareSolverr会话失败: {"status": "error", "message": "Error: The session doesn't exist.", "startTimestamp": 1753857557024, "endTimestamp": 1753857557024, "version": "3.3.21"}
2025-07-30 14:39:08 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:39:08 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 8545e2c2-025a-4f96-8d5c-80061b5e9aed
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:destroy_session:81 - 销毁FlareSolverr会话失败: {"status": "error", "message": "Error: The session doesn't exist.", "startTimestamp": 1753857557126, "endTimestamp": 1753857557126, "version": "3.3.21"}
2025-07-30 14:39:08 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: user7r8grjse
2025-07-30 14:39:08 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:39:08 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:39:08 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:39:08 | DEBUG    | core.data_manager:save_task:32 - 保存任务: da89d5cd-ffda-485d-a894-0fa6ca61b2c8
2025-07-30 14:39:08 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: usertaw94r2v
2025-07-30 14:39:08 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:39:08 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:39:08 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:39:08 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:39:55 | INFO     | core.http_client:close:61 - HTTP客户端已关闭
2025-07-30 14:40:39 | INFO     | services.email_service_factory:register_service:24 - 注册邮箱服务: linshiguge
2025-07-30 14:40:39 | INFO     | services.email_service_factory:initialize_services:54 - 使用FlareSolverr版本的邮箱服务
2025-07-30 14:40:39 | INFO     | services.email_service_factory:initialize_services:60 - 已初始化 1 个邮箱服务
2025-07-30 14:40:47 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 14:40:47 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 5 个账户
2025-07-30 14:40:47 | INFO     | services.email_service_factory:register_service:24 - 注册邮箱服务: linshiguge
2025-07-30 14:40:47 | INFO     | services.email_service_factory:initialize_services:54 - 使用FlareSolverr版本的邮箱服务
2025-07-30 14:40:47 | INFO     | services.email_service_factory:initialize_services:60 - 已初始化 1 个邮箱服务
2025-07-30 14:40:47 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userbka68u9p
2025-07-30 14:40:47 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userub6j0523
2025-07-30 14:40:47 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userllmu4apo
2025-07-30 14:40:47 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: user2vyfqcnh
2025-07-30 14:40:47 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: usernxtljc6b
2025-07-30 14:40:47 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 5 个
2025-07-30 14:40:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: ab854f8e-3baa-4e95-a231-5ebb703914bc
2025-07-30 14:40:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 712236b6-f6c1-4c89-bf0c-c23962bb8a83
2025-07-30 14:40:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 82695cc8-4d5b-4b0b-aa2d-3e83712c75bb
2025-07-30 14:40:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: ecdd4f48-d125-4bc2-b47a-a10f43817947
2025-07-30 14:40:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 6c43a99d-0003-411d-96ef-b28a988326f2
2025-07-30 14:40:47 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 5 个注册任务
2025-07-30 14:40:47 | INFO     | core.http_client:start:51 - HTTP客户端启动成功
2025-07-30 14:40:47 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userbka68u9p
2025-07-30 14:40:47 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:40:47 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:40:47 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userub6j0523
2025-07-30 14:40:47 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:40:47 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:40:47 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userllmu4apo
2025-07-30 14:40:47 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:40:47 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:42:47 | ERROR    | core.flaresolverr_client:check_health:219 - 检查FlareSolverr服务失败: 
2025-07-30 14:42:47 | ERROR    | services.linshiguge_flaresolverr_service:create_email:34 - FlareSolverr服务不可用
2025-07-30 14:42:47 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:42:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: ab854f8e-3baa-4e95-a231-5ebb703914bc
2025-07-30 14:42:47 | ERROR    | core.flaresolverr_client:check_health:219 - 检查FlareSolverr服务失败: 
2025-07-30 14:42:47 | ERROR    | services.linshiguge_flaresolverr_service:create_email:34 - FlareSolverr服务不可用
2025-07-30 14:42:47 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:42:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 82695cc8-4d5b-4b0b-aa2d-3e83712c75bb
2025-07-30 14:42:47 | ERROR    | core.flaresolverr_client:check_health:219 - 检查FlareSolverr服务失败: 
2025-07-30 14:42:47 | ERROR    | services.linshiguge_flaresolverr_service:create_email:34 - FlareSolverr服务不可用
2025-07-30 14:42:47 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:42:47 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 712236b6-f6c1-4c89-bf0c-c23962bb8a83
2025-07-30 14:42:47 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: user2vyfqcnh
2025-07-30 14:42:47 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:42:47 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:42:47 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: usernxtljc6b
2025-07-30 14:42:47 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:42:47 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:43:06 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:43:06 | INFO     | core.flaresolverr_client:check_health:212 - FlareSolverr服务运行正常
2025-07-30 14:43:15 | INFO     | core.http_client:close:61 - HTTP客户端已关闭
2025-07-30 14:45:01 | INFO     | services.email_service_factory:register_service:24 - 注册邮箱服务: linshiguge
2025-07-30 14:45:01 | INFO     | services.email_service_factory:initialize_services:54 - 使用FlareSolverr版本的邮箱服务
2025-07-30 14:45:01 | INFO     | services.email_service_factory:initialize_services:60 - 已初始化 1 个邮箱服务
2025-07-30 14:45:38 | INFO     | __main__:main:71 - 开始批量注册...
2025-07-30 14:45:38 | INFO     | core.registration_controller:start_batch_registration:41 - 开始批量注册: 5 个账户
2025-07-30 14:45:38 | INFO     | services.email_service_factory:register_service:24 - 注册邮箱服务: linshiguge
2025-07-30 14:45:38 | INFO     | services.email_service_factory:initialize_services:54 - 使用FlareSolverr版本的邮箱服务
2025-07-30 14:45:38 | INFO     | services.email_service_factory:initialize_services:60 - 已初始化 1 个邮箱服务
2025-07-30 14:45:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: user64b14uf3
2025-07-30 14:45:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userkjjjode4
2025-07-30 14:45:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userko6zsn5z
2025-07-30 14:45:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userpvmn8huj
2025-07-30 14:45:38 | DEBUG    | utils.user_generator:generate_username:29 - 生成用户名: userj0rmtuxt
2025-07-30 14:45:38 | INFO     | utils.user_generator:generate_usernames:50 - 批量生成用户名: 5 个
2025-07-30 14:45:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: e1a52b9e-17fd-4dca-9a97-b1eb167b4712
2025-07-30 14:45:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 05704867-1308-4aa8-b8b7-15a5ad9981c6
2025-07-30 14:45:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: b2a64c82-52f4-46ff-a65f-5ed058fe5124
2025-07-30 14:45:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: e0aaab8e-164e-41d6-b3a0-0daf804bd2d4
2025-07-30 14:45:38 | DEBUG    | core.data_manager:save_task:32 - 保存任务: f24334b4-4534-4544-b912-a4824c7e9a94
2025-07-30 14:45:38 | INFO     | core.registration_controller:_create_registration_tasks:105 - 创建了 5 个注册任务
2025-07-30 14:45:38 | INFO     | core.http_client:start:51 - HTTP客户端启动成功
2025-07-30 14:45:38 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: user64b14uf3
2025-07-30 14:45:38 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:45:38 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:45:38 | DEBUG    | core.flaresolverr_client:check_health:209 - 检查FlareSolverr服务健康状态: http://111.231.167.224:8191/health
2025-07-30 14:45:38 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userkjjjode4
2025-07-30 14:45:38 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:45:38 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:45:38 | DEBUG    | core.flaresolverr_client:check_health:209 - 检查FlareSolverr服务健康状态: http://111.231.167.224:8191/health
2025-07-30 14:45:38 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userko6zsn5z
2025-07-30 14:45:38 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:45:38 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:45:38 | DEBUG    | core.flaresolverr_client:check_health:209 - 检查FlareSolverr服务健康状态: http://111.231.167.224:8191/health
2025-07-30 14:45:40 | DEBUG    | core.flaresolverr_client:check_health:212 - FlareSolverr健康检查响应: 200
2025-07-30 14:45:40 | INFO     | core.flaresolverr_client:check_health:214 - FlareSolverr服务运行正常
2025-07-30 14:45:40 | DEBUG    | core.flaresolverr_client:check_health:212 - FlareSolverr健康检查响应: 200
2025-07-30 14:45:40 | INFO     | core.flaresolverr_client:check_health:214 - FlareSolverr服务运行正常
2025-07-30 14:45:40 | DEBUG    | core.flaresolverr_client:check_health:212 - FlareSolverr健康检查响应: 200
2025-07-30 14:45:40 | INFO     | core.flaresolverr_client:check_health:214 - FlareSolverr服务运行正常
2025-07-30 14:47:40 | ERROR    | core.flaresolverr_client:create_session:55 - 创建FlareSolverr会话异常: 
2025-07-30 14:47:40 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:47:40 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:47:40 | DEBUG    | core.data_manager:save_task:32 - 保存任务: 05704867-1308-4aa8-b8b7-15a5ad9981c6
2025-07-30 14:47:40 | ERROR    | core.flaresolverr_client:create_session:55 - 创建FlareSolverr会话异常: 
2025-07-30 14:47:40 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:47:40 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:47:40 | DEBUG    | core.data_manager:save_task:32 - 保存任务: e1a52b9e-17fd-4dca-9a97-b1eb167b4712
2025-07-30 14:47:40 | ERROR    | core.flaresolverr_client:create_session:55 - 创建FlareSolverr会话异常: 
2025-07-30 14:47:40 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:47:40 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:47:40 | DEBUG    | core.data_manager:save_task:32 - 保存任务: b2a64c82-52f4-46ff-a65f-5ed058fe5124
2025-07-30 14:47:40 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userpvmn8huj
2025-07-30 14:47:40 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:47:40 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:47:40 | DEBUG    | core.flaresolverr_client:check_health:209 - 检查FlareSolverr服务健康状态: http://111.231.167.224:8191/health
2025-07-30 14:47:40 | INFO     | core.registration_controller:_register_single_user:146 - 开始注册用户: userj0rmtuxt
2025-07-30 14:47:40 | INFO     | core.registration_controller:_create_temp_email:185 - 使用邮箱服务: linshiguge_flaresolverr
2025-07-30 14:47:40 | INFO     | services.linshiguge_flaresolverr_service:create_email:28 - 开始创建linshiguge_flaresolverr邮箱
2025-07-30 14:47:40 | DEBUG    | core.flaresolverr_client:check_health:209 - 检查FlareSolverr服务健康状态: http://111.231.167.224:8191/health
2025-07-30 14:48:15 | INFO     | core.http_client:close:61 - HTTP客户端已关闭
