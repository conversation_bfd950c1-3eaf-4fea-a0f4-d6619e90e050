2025-07-30 11:38:38 | ERROR    | core.http_client:start:49 - HTTP客户端启动失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:38:38 | ERROR    | core.registration_controller:start_batch_registration:65 - 批量注册失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:41:25 | ERROR    | core.http_client:start:49 - HTTP客户端启动失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:41:25 | ERROR    | core.registration_controller:start_batch_registration:65 - 批量注册失败: __init__() got an unexpected keyword argument 'proxy'
2025-07-30 11:51:21 | ERROR    | services.linshiguge_service:create_email:67 - 无法获取邮箱地址
2025-07-30 11:51:21 | ERROR    | __main__:test_email_service:43 - ❌ 邮箱创建失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:solve_challenge:139 - FlareSolverr请求失败: HTTP 500
2025-07-30 14:39:08 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:solve_challenge:139 - FlareSolverr请求失败: HTTP 500
2025-07-30 14:39:08 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:solve_challenge:139 - FlareSolverr请求失败: HTTP 500
2025-07-30 14:39:08 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:destroy_session:81 - 销毁FlareSolverr会话失败: {"status": "error", "message": "Error: The session doesn't exist.", "startTimestamp": 1753857557024, "endTimestamp": 1753857557024, "version": "3.3.21"}
2025-07-30 14:39:08 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:39:08 | ERROR    | core.flaresolverr_client:destroy_session:81 - 销毁FlareSolverr会话失败: {"status": "error", "message": "Error: The session doesn't exist.", "startTimestamp": 1753857557126, "endTimestamp": 1753857557126, "version": "3.3.21"}
2025-07-30 14:39:08 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:42:47 | ERROR    | core.flaresolverr_client:check_health:219 - 检查FlareSolverr服务失败: 
2025-07-30 14:42:47 | ERROR    | services.linshiguge_flaresolverr_service:create_email:34 - FlareSolverr服务不可用
2025-07-30 14:42:47 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:42:47 | ERROR    | core.flaresolverr_client:check_health:219 - 检查FlareSolverr服务失败: 
2025-07-30 14:42:47 | ERROR    | services.linshiguge_flaresolverr_service:create_email:34 - FlareSolverr服务不可用
2025-07-30 14:42:47 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:42:47 | ERROR    | core.flaresolverr_client:check_health:219 - 检查FlareSolverr服务失败: 
2025-07-30 14:42:47 | ERROR    | services.linshiguge_flaresolverr_service:create_email:34 - FlareSolverr服务不可用
2025-07-30 14:42:47 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:47:40 | ERROR    | core.flaresolverr_client:create_session:55 - 创建FlareSolverr会话异常: 
2025-07-30 14:47:40 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:47:40 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:47:40 | ERROR    | core.flaresolverr_client:create_session:55 - 创建FlareSolverr会话异常: 
2025-07-30 14:47:40 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:47:40 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
2025-07-30 14:47:40 | ERROR    | core.flaresolverr_client:create_session:55 - 创建FlareSolverr会话异常: 
2025-07-30 14:47:40 | ERROR    | services.linshiguge_flaresolverr_service:create_email:44 - FlareSolverr解决Cloudflare挑战失败
2025-07-30 14:47:40 | ERROR    | core.registration_controller:_create_temp_email:201 - 创建临时邮箱失败: 创建邮箱失败
