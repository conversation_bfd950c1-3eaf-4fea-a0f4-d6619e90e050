# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from http.cookiejar import Cookie, <PERSON>ie<PERSON>ar
from typing import Union

from .._base.browser import Chromium
from .._pages.chromium_base import ChromiumBase
from .._pages.tabs import MixTab
from .._pages.session_page import SessionPage
from .._pages.mix_page import MixPage


class BrowserCookiesSetter(object):
    _owner: Chromium = ...

    def __init__(self, page: Chromium): ...

    def __call__(self, cookies: Union[CookieJar, Cookie, list, tuple, str, dict]) -> None: ...

    def clear(self) -> None: ...


class CookiesSetter(BrowserCookiesSetter):
    _owner: ChromiumBase = ...

    def __init__(self, page: ChromiumBase): ...

    def remove(self, name: str, url: str = None, domain: str = None, path: str = None) -> None: ...

    def clear(self) -> None: ...


class SessionCookiesSetter(object):
    _owner: SessionPage = ...

    def __init__(self, page: SessionPage): ...

    def __call__(self, cookies: Union[CookieJar, Cookie, list, tuple, str, dict]) -> None: ...

    def remove(self, name: str) -> None: ...

    def clear(self) -> None: ...


class MixPageCookiesSetter(CookiesSetter, SessionCookiesSetter):
    _owner: Union[MixPage, MixTab] = ...

    def __init__(self, page: SessionPage): ...

    def __call__(self, cookies: Union[CookieJar, Cookie, list, tuple, str, dict]) -> None: ...

    def remove(self, name: str, url: str = None, domain: str = None, path: str = None) -> None: ...

    def clear(self) -> None: ...
