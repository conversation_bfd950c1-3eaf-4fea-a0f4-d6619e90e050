"""
邮箱服务抽象基类
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from core.models import EmailInfo
from utils.logger import app_logger


class EmailServiceBase(ABC):
    """邮箱服务抽象基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.url = config.get("url", "")
        self.enabled = config.get("enabled", True)
        self.priority = config.get("priority", 999)
    
    @abstractmethod
    async def create_email(self) -> Optional[EmailInfo]:
        """
        创建临时邮箱

        Returns:
            EmailInfo: 邮箱信息，失败返回None
        """
        pass

    @abstractmethod
    async def get_verification_code(self, email_info: EmailInfo, timeout: int = 60) -> Optional[str]:
        """
        获取验证码

        Args:
            email_info: 邮箱信息
            timeout: 超时时间(秒)

        Returns:
            str: 验证码，失败返回None
        """
        pass

    
    def __str__(self) -> str:
        return f"EmailService({self.name}, enabled={self.enabled}, priority={self.priority})"
