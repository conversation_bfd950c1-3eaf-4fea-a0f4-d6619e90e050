# 批量注册工具

基于Playwright的智能批量注册工具，支持Cloudflare绕过和多种临时邮箱服务。

## ✨ 特性

- 🚀 **高性能**: 基于异步编程，支持高并发批量注册
- 🛡️ **反检测**: 集成Playwright，支持Cloudflare绕过和浏览器指纹伪装
- 📧 **多邮箱服务**: 支持多种临时邮箱服务，自动切换和故障转移
- ⚡ **智能重试**: 内置重试机制，处理网络错误和API限制
- 📊 **数据管理**: 完整的数据持久化和统计分析功能
- 🔧 **灵活配置**: 支持多种配置方式，适应不同使用场景

## 🏗️ 架构设计

```
batch_register/
├── config/              # 配置文件
│   └── settings.py      # 应用配置
├── core/               # 核心模块
│   ├── browser_manager.py      # 浏览器管理
│   ├── data_manager.py         # 数据管理
│   ├── http_client.py          # HTTP客户端
│   ├── models.py               # 数据模型
│   └── registration_controller.py  # 注册控制器
├── services/           # 邮箱服务
│   ├── email_service_base.py   # 服务基类
│   ├── email_service_factory.py # 服务工厂
│   └── linshiguge_service.py   # 临时谷歌邮箱服务
├── utils/              # 工具模块
│   ├── logger.py       # 日志系统
│   └── user_generator.py # 用户数据生成
├── data/               # 数据存储
├── logs/               # 日志文件
└── main.py             # 主程序入口
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Windows/Linux/macOS

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动FlareSolverr服务（推荐）

FlareSolverr用于绕过Cloudflare保护，提供更稳定的服务：

```bash
# 方法一：使用Docker Compose（推荐）
# 复制环境配置文件
cp .env.docker .env
# 启动服务
docker-compose up -d

# 方法二：直接使用Docker（官方镜像）
docker run -d --name flaresolverr -p 8191:8191 ghcr.io/flaresolverr/flaresolverr:latest

# 方法三：使用华为云镜像（推荐国内用户）
docker run -d --name flaresolverr -p 8191:8191 swr.cn-north-4.myhuaweicloud.com/ddn-k8s/ghcr.io/flaresolverr/flaresolverr:latest

# 检查服务状态
curl http://localhost:8191/health
```

### 4. 配置环境

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

### 5. 运行程序

```bash
# 基础用法
python main.py --count 10

# 自定义配置
python main.py --count 20 --username-prefix test --max-concurrent 5

# 查看统计信息
python main.py --stats

# 导出结果
python main.py --export json
```

## 📋 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--count` | 注册数量 | 5 |
| `--username-prefix` | 用户名前缀 | user |
| `--password` | 密码 | qwer123321 |
| `--aff-code` | 邀请码 | p6rP |
| `--email-service` | 邮箱服务 | linshiguge |
| `--max-concurrent` | 最大并发数 | 3 |
| `--request-delay` | 请求间隔(秒) | 2.0 |
| `--stats` | 显示统计信息 | - |
| `--export` | 导出结果(json/csv) | - |
| `--clear` | 清理数据(all/tasks/results) | - |

## 🔧 配置说明

### 邮箱服务配置

在 `config/settings.py` 中配置支持的邮箱服务：

```python
EMAIL_SERVICES = {
    "linshiguge": {
        "url": "https://www.linshiguge.com/",
        "enabled": True,
        "priority": 1
    }
}
```

### 代理配置

支持HTTP代理，在 `.env` 文件中配置：

```env
USE_PROXY=true
PROXY_LIST=http://proxy1:port,http://proxy2:port
```

### FlareSolverr配置

推荐使用FlareSolverr绕过Cloudflare：

```env
USE_FLARESOLVERR=true                    # 启用FlareSolverr
FLARESOLVERR_URL=http://localhost:8191   # FlareSolverr服务地址
FLARESOLVERR_TIMEOUT=60000               # 超时时间(毫秒)
```

### 浏览器配置

支持多种浏览器引擎（FlareSolverr禁用时使用）：

```env
BROWSER_TYPE=chromium  # chromium, firefox, webkit
HEADLESS=true          # 无头模式
```

## 📊 数据管理

### 查看统计信息

```bash
python main.py --stats
```

### 导出结果

```bash
# 导出为JSON
python main.py --export json

# 导出为CSV
python main.py --export csv
```

### 清理数据

```bash
# 清理所有数据
python main.py --clear all

# 只清理任务数据
python main.py --clear tasks

# 只清理结果数据
python main.py --clear results
```

## 🛡️ 反检测机制

### FlareSolverr集成（推荐）

- **专业Cloudflare绕过**：使用FlareSolverr专门处理Cloudflare保护
- **高成功率**：比传统浏览器自动化更稳定
- **资源优化**：无需启动完整浏览器，性能更好
- **简单部署**：Docker一键启动

### Playwright备选方案

- 真实浏览器环境模拟
- 自动处理JavaScript挑战
- 智能等待机制

### 浏览器指纹伪装

- 随机User-Agent轮换
- 浏览器属性伪装
- 行为模拟

### 请求频率控制

- 智能延迟机制
- 并发数量限制
- 代理IP轮换

## 📝 日志系统

日志文件位于 `logs/` 目录：

- `app.log`: 应用日志
- `error.log`: 错误日志

日志级别可通过环境变量 `LOG_LEVEL` 配置。

## ⚠️ 注意事项

1. **合规使用**: 请确保使用本工具符合相关法律法规和网站服务条款
2. **频率控制**: 建议设置合理的并发数和请求间隔，避免对目标服务器造成压力
3. **数据备份**: 重要数据请及时备份，避免意外丢失
4. **网络环境**: 建议在稳定的网络环境下使用，必要时配置代理

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持基础批量注册功能
- 集成Playwright浏览器自动化
- 支持临时谷歌邮箱服务
- 完整的数据管理和统计功能

## 📄 许可证

本项目仅供学习和研究使用，请勿用于非法用途。
