# 批量注册工具环境配置示例
# 复制此文件为 .env 并修改相应配置

# 基础配置
DEBUG=false
LOG_LEVEL=INFO

# 注册API配置
API_BASE_URL=https://code.wenwen-ai.com
VERIFICATION_ENDPOINT=/api/verification
REGISTER_ENDPOINT=/api/user/register

# 邀请码配置 (多个邀请码用逗号分隔)
AFF_CODES=p6rP,code2,code3

# 用户代理配置 (多个用|分隔，不设置则使用默认)
# USER_AGENTS=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36

# 浏览器配置
BROWSER_TYPE=chromium
HEADLESS=true
BROWSER_POOL_SIZE=5

# 代理配置
USE_PROXY=false
# PROXY_LIST=http://proxy1:port,http://proxy2:port

# 并发配置
MAX_CONCURRENT=3
REQUEST_DELAY=2.0

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=5.0

# FlareSolverr配置
USE_FLARESOLVERR=true
# 云服务器部署
FLARESOLVERR_URL=http://***************:8191
# 本地部署
# FLARESOLVERR_URL=http://localhost:8191
FLARESOLVERR_TIMEOUT=60000

# 数据存储配置
DATA_DIR=data
RESULTS_FILE=registration_results.json
