# FlareSolverr Docker配置
# 复制此文件为 .env 并根据需要修改

# 镜像选择（国内用户推荐使用国内镜像）
# 官方镜像（可能较慢）
FLARESOLVERR_IMAGE=ghcr.io/flaresolverr/flaresolverr:latest
# DockerHub镜像
# FLARESOLVERR_IMAGE=flaresolverr/flaresolverr:latest
# 阿里云镜像（推荐）
# FLARESOLVERR_IMAGE=registry.cn-hangzhou.aliyuncs.com/flaresolverr/flaresolverr:latest
# 腾讯云镜像
# FLARESOLVERR_IMAGE=ccr.ccs.tencentyun.com/flaresolverr/flaresolverr:latest

# 端口配置
PORT=8191

# 日志级别: debug, info, warn, error
LOG_LEVEL=info

# 是否记录HTML内容到日志
LOG_HTML=false

# 验证码解决方案: none, hcaptcha-solver, recaptcha-solver
CAPTCHA_SOLVER=none
