# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from pathlib import Path
from typing import Union, Any, Literal, Optional, Tuple


class ChromiumOptions(object):
    ini_path: Optional[str] = ...
    _driver_path: str = ...
    _user_data_path: Optional[str] = ...
    _download_path: str = ...
    _tmp_path: str = ...
    _arguments: list = ...
    _browser_path: str = ...
    _user: str = ...
    _load_mode: str = ...
    _timeouts: dict = ...
    _proxy: str = ...
    _address: str = ...
    _extensions: list = ...
    _prefs: dict = ...
    _flags: dict = ...
    _prefs_to_del: list = ...
    _new_env: bool = ...
    clear_file_flags: bool = ...
    _auto_port: Union[Tuple[int, int], False] = ...
    _system_user_path: bool = ...
    _existing_only: bool = ...
    _retry_times: int = ...
    _retry_interval: float = ...
    _is_headless: bool = ...
    _ua_set: bool = ...

    def __init__(self, read_file: [bool, None] = True, ini_path: Union[str, Path] = None): ...

    @property
    def download_path(self) -> str: ...

    @property
    def browser_path(self) -> str: ...

    @property
    def user_data_path(self) -> str: ...

    @property
    def tmp_path(self) -> Optional[str]: ...

    @property
    def user(self) -> str: ...

    @property
    def load_mode(self) -> str: ...

    @property
    def timeouts(self) -> dict: ...

    @property
    def proxy(self) -> str: ...

    @property
    def address(self) -> str: ...

    @property
    def arguments(self) -> list: ...

    @property
    def extensions(self) -> list: ...

    @property
    def preferences(self) -> dict: ...

    @property
    def flags(self) -> dict: ...

    @property
    def system_user_path(self) -> bool: ...

    @property
    def is_existing_only(self) -> bool: ...

    @property
    def is_auto_port(self) -> Union[bool, Tuple[int, int]]: ...

    @property
    def retry_times(self) -> int: ...

    @property
    def retry_interval(self) -> float: ...

    @property
    def is_headless(self) -> bool: ...

    def set_retry(self, times: int = None, interval: float = None) -> ChromiumOptions: ...

    def set_argument(self, arg: str, value: Union[str, None, bool] = None) -> ChromiumOptions: ...

    def remove_argument(self, value: str) -> ChromiumOptions: ...

    def add_extension(self, path: Union[str, Path]) -> ChromiumOptions: ...

    def remove_extensions(self) -> ChromiumOptions: ...

    def set_pref(self, arg: str, value: Any) -> ChromiumOptions: ...

    def remove_pref(self, arg: str) -> ChromiumOptions: ...

    def remove_pref_from_file(self, arg: str) -> ChromiumOptions: ...

    def set_flag(self, flag: str, value: Union[int, str, bool] = None) -> ChromiumOptions: ...

    def clear_flags_in_file(self) -> ChromiumOptions: ...

    def clear_flags(self) -> ChromiumOptions: ...

    def clear_arguments(self) -> ChromiumOptions: ...

    def clear_prefs(self) -> ChromiumOptions: ...

    def set_timeouts(self,
                     base: float = None,
                     page_load: float = None,
                     script: float = None) -> ChromiumOptions: ...

    def set_user(self, user: str = 'Default') -> ChromiumOptions: ...

    def headless(self, on_off: bool = True) -> ChromiumOptions: ...

    def no_imgs(self, on_off: bool = True) -> ChromiumOptions: ...

    def no_js(self, on_off: bool = True) -> ChromiumOptions: ...

    def mute(self, on_off: bool = True) -> ChromiumOptions: ...

    def incognito(self, on_off: bool = True) -> ChromiumOptions: ...

    def new_env(self, on_off: bool = True) -> ChromiumOptions: ...

    def set_user_agent(self, user_agent: str) -> ChromiumOptions: ...

    def set_proxy(self, proxy: str) -> ChromiumOptions: ...

    def ignore_certificate_errors(self, on_off=True) -> ChromiumOptions: ...

    def set_load_mode(self, value: Literal['normal', 'eager', 'none']) -> ChromiumOptions: ...

    def set_browser_path(self, path: Union[str, Path]) -> ChromiumOptions: ...

    def set_local_port(self, port: Union[str, int]) -> ChromiumOptions: ...

    def set_address(self, address: str) -> ChromiumOptions: ...

    def set_download_path(self, path: Union[str, Path]) -> ChromiumOptions: ...

    def set_tmp_path(self, path: Union[str, Path]) -> ChromiumOptions: ...

    def set_user_data_path(self, path: Union[str, Path]) -> ChromiumOptions: ...

    def set_cache_path(self, path: Union[str, Path]) -> ChromiumOptions: ...

    def set_paths(self, browser_path: Union[str, Path] = None, local_port: Union[int, str] = None,
                  address: str = None, download_path: Union[str, Path] = None, user_data_path: Union[str, Path] = None,
                  cache_path: Union[str, Path] = None) -> ChromiumOptions: ...

    def use_system_user_path(self, on_off: bool = True) -> ChromiumOptions: ...

    def auto_port(self,
                  on_off: bool = True,
                  scope: Tuple[int, int] = None) -> ChromiumOptions: ...

    def existing_only(self, on_off: bool = True) -> ChromiumOptions: ...

    def save(self, path: Union[str, Path] = None) -> str: ...

    def save_to_default(self) -> str: ...
