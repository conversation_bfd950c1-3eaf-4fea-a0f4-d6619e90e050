"""
批量注册控制器
"""
import asyncio
import uuid
import random
from typing import List, Optional, Dict, Any
from datetime import datetime
from asyncio_throttle import Throttler

from core.models import (
    RegistrationTask, RegistrationResult, RegistrationStatus, 
    BatchRegistrationConfig, UserInfo, EmailInfo
)

from core.http_client import RegistrationApiClient
from core.data_manager import data_manager
from services.email_service_factory import EmailServiceFactory
from utils.user_generator import UserGenerator
from utils.logger import app_logger
from config.settings import settings


class RegistrationController:
    """批量注册控制器"""
    
    def __init__(self):
        self.running = False
        self.tasks: List[RegistrationTask] = []
        self.throttler = Throttler(rate_limit=settings.MAX_CONCURRENT)
        self.api_client: Optional[RegistrationApiClient] = None
    
    async def start_batch_registration(self, config: BatchRegistrationConfig) -> bool:
        """开始批量注册"""
        try:
            if self.running:
                app_logger.warning("批量注册已在运行中")
                return False
            
            self.running = True
            app_logger.info(f"开始批量注册: {config.count} 个账户")
            
            # 初始化邮箱服务工厂
            EmailServiceFactory.initialize_services()
            
            # 创建注册任务
            tasks = await self._create_registration_tasks(config)
            if not tasks:
                app_logger.error("创建注册任务失败")
                return False
            
            self.tasks = tasks
            
            # 启动API客户端
            self.api_client = RegistrationApiClient()
            await self.api_client.__aenter__()
            
            # 执行批量注册
            await self._execute_batch_registration(config)
            
            app_logger.info("批量注册完成")
            return True
            
        except Exception as e:
            app_logger.error(f"批量注册失败: {e}")
            return False
        finally:
            self.running = False
            await self._cleanup()
    
    async def _create_registration_tasks(self, config: BatchRegistrationConfig) -> List[RegistrationTask]:
        """创建注册任务"""
        try:
            tasks = []
            usernames = UserGenerator.generate_usernames(config.count, config.username_prefix)
            
            for i, username in enumerate(usernames):
                # 选择邀请码
                aff_code = random.choice(config.aff_codes)
                
                # 创建邮箱信息占位符
                email_info = EmailInfo(
                    email="",  # 稍后创建
                    service=config.email_service
                )
                
                # 创建用户信息
                user_info = UserInfo(
                    username=username,
                    password=config.password,
                    email_info=email_info,
                    aff_code=aff_code
                )
                
                # 创建注册任务
                task = RegistrationTask(
                    id=str(uuid.uuid4()),
                    user_info=user_info,
                    status=RegistrationStatus.PENDING
                )
                
                tasks.append(task)
                await data_manager.save_task(task)
            
            app_logger.info(f"创建了 {len(tasks)} 个注册任务")
            return tasks
            
        except Exception as e:
            app_logger.error(f"创建注册任务失败: {e}")
            return []
    
    async def _execute_batch_registration(self, config: BatchRegistrationConfig):
        """执行批量注册"""
        try:
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(config.max_concurrent)
            
            # 创建任务协程
            coroutines = []
            for task in self.tasks:
                coroutine = self._process_single_registration(task, semaphore, config)
                coroutines.append(coroutine)
            
            # 并发执行所有任务
            await asyncio.gather(*coroutines, return_exceptions=True)
            
        except Exception as e:
            app_logger.error(f"执行批量注册失败: {e}")
    
    async def _process_single_registration(self, task: RegistrationTask, 
                                         semaphore: asyncio.Semaphore,
                                         config: BatchRegistrationConfig):
        """处理单个注册任务"""
        async with semaphore:
            try:
                await self._register_single_user(task, config)
            except Exception as e:
                app_logger.error(f"处理注册任务失败 {task.id}: {e}")
                task.update_status(RegistrationStatus.FAILED, str(e))
                await data_manager.update_task(task)
                await self._save_failed_result(task, str(e))
    
    async def _register_single_user(self, task: RegistrationTask, config: BatchRegistrationConfig):
        """注册单个用户"""
        try:
            app_logger.info(f"开始注册用户: {task.user_info.username}")
            
            # 步骤1: 创建临时邮箱
            if not await self._create_temp_email(task):
                return
            
            # 步骤2: 发送验证码
            if not await self._send_verification_code(task):
                return
            
            # 步骤3: 获取验证码
            if not await self._get_verification_code(task):
                return
            
            # 步骤4: 注册用户
            if not await self._register_user(task):
                return
            
            # 注册成功
            await self._save_success_result(task)
            app_logger.info(f"用户注册成功: {task.user_info.username}")
            
        except Exception as e:
            app_logger.error(f"注册用户失败 {task.user_info.username}: {e}")
            task.update_status(RegistrationStatus.FAILED, str(e))
            await data_manager.update_task(task)
            await self._save_failed_result(task, str(e))
    
    async def _create_temp_email(self, task: RegistrationTask) -> bool:
        """创建临时邮箱"""
        try:
            # 获取邮箱服务
            email_service = EmailServiceFactory.get_service(task.user_info.email_info.service)
            if not email_service:
                email_service = EmailServiceFactory.get_best_service()
            
            if not email_service:
                raise Exception("没有可用的邮箱服务")
            
            app_logger.info(f"使用邮箱服务: {email_service.name}")
            
            # 创建邮箱（FlareSolverr服务不需要浏览器上下文）
            email_info = await email_service.create_email()
            if not email_info:
                raise Exception("创建邮箱失败")

            # 更新任务
            task.user_info.email_info = email_info
            task.update_status(RegistrationStatus.PENDING)
            await data_manager.update_task(task)

            app_logger.info(f"创建邮箱成功: {email_info.email}")
            return True
                
        except Exception as e:
            app_logger.error(f"创建临时邮箱失败: {e}")
            task.update_status(RegistrationStatus.FAILED, f"创建邮箱失败: {e}")
            await data_manager.update_task(task)
            return False
    
    async def _send_verification_code(self, task: RegistrationTask) -> bool:
        """发送验证码"""
        try:
            if not self.api_client:
                raise Exception("API客户端未初始化")
            
            email = task.user_info.email_info.email
            success = await self.api_client.send_verification_code(email)
            
            if success:
                task.update_status(RegistrationStatus.EMAIL_SENT)
                await data_manager.update_task(task)
                
                # 添加延迟
                await asyncio.sleep(random.uniform(1, 3))
                return True
            else:
                raise Exception("发送验证码失败")
                
        except Exception as e:
            app_logger.error(f"发送验证码失败: {e}")
            task.update_status(RegistrationStatus.FAILED, f"发送验证码失败: {e}")
            await data_manager.update_task(task)
            return False
    
    async def _get_verification_code(self, task: RegistrationTask) -> bool:
        """获取验证码"""
        try:
            # 获取邮箱服务
            email_service = EmailServiceFactory.get_service(task.user_info.email_info.service)
            if not email_service:
                raise Exception("邮箱服务不可用")
            
            # 获取验证码（FlareSolverr服务不需要浏览器上下文）
            verification_code = await email_service.get_verification_code(
                task.user_info.email_info, timeout=60
            )

            if verification_code:
                task.user_info.email_info.verification_code = verification_code
                task.update_status(RegistrationStatus.CODE_RECEIVED)
                await data_manager.update_task(task)

                app_logger.info(f"获取验证码成功: {verification_code}")
                return True
            else:
                raise Exception("获取验证码超时")
                
        except Exception as e:
            app_logger.error(f"获取验证码失败: {e}")
            task.update_status(RegistrationStatus.FAILED, f"获取验证码失败: {e}")
            await data_manager.update_task(task)
            return False
    
    async def _register_user(self, task: RegistrationTask) -> bool:
        """注册用户"""
        try:
            if not self.api_client:
                raise Exception("API客户端未初始化")
            
            task.update_status(RegistrationStatus.REGISTERING)
            await data_manager.update_task(task)
            
            user_info = task.user_info
            result = await self.api_client.register_user(
                username=user_info.username,
                password=user_info.password,
                email=user_info.email_info.email,
                verification_code=user_info.email_info.verification_code,
                aff_code=user_info.aff_code
            )
            
            if result["success"]:
                task.update_status(RegistrationStatus.SUCCESS)
                await data_manager.update_task(task)
                return True
            else:
                raise Exception(result["message"])
                
        except Exception as e:
            app_logger.error(f"注册用户失败: {e}")
            task.update_status(RegistrationStatus.FAILED, f"注册失败: {e}")
            await data_manager.update_task(task)
            return False
    
    async def _save_success_result(self, task: RegistrationTask):
        """保存成功结果"""
        result = RegistrationResult(
            task_id=task.id,
            username=task.user_info.username,
            email=task.user_info.email_info.email,
            status=RegistrationStatus.SUCCESS,
            success=True,
            created_at=task.created_at,
            completed_at=datetime.now(),
            retry_count=task.retry_count
        )
        await data_manager.save_result(result)
    
    async def _save_failed_result(self, task: RegistrationTask, error_message: str):
        """保存失败结果"""
        result = RegistrationResult(
            task_id=task.id,
            username=task.user_info.username,
            email=task.user_info.email_info.email,
            status=task.status,
            success=False,
            created_at=task.created_at,
            completed_at=datetime.now(),
            error_message=error_message,
            retry_count=task.retry_count
        )
        await data_manager.save_result(result)
    
    async def _cleanup(self):
        """清理资源"""
        try:
            if self.api_client:
                await self.api_client.__aexit__(None, None, None)
            
            # FlareSolverr服务不需要清理浏览器资源
            pass
            
        except Exception as e:
            app_logger.error(f"清理资源失败: {e}")
    
    async def get_progress(self) -> Dict[str, Any]:
        """获取进度信息"""
        try:
            stats = await data_manager.get_statistics()
            return {
                "running": self.running,
                "total_tasks": len(self.tasks),
                "statistics": stats
            }
        except Exception as e:
            app_logger.error(f"获取进度失败: {e}")
            return {"running": self.running, "error": str(e)}


# 全局注册控制器实例
registration_controller = RegistrationController()
