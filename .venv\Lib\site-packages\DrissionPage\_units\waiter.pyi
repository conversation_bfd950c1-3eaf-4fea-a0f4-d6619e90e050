# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from typing import Union, Tuple, List

from .downloader import DownloadMission
from .._base.browser import Chromium
from .._elements.chromium_element import ChromiumElement
from .._pages.chromium_base import ChromiumBase
from .._pages.chromium_frame import ChromiumFrame
from .._pages.chromium_page import ChromiumPage
from .._pages.mix_page import MixPage
from .._pages.tabs import ChromiumTab, MixTab


class OriginWaiter(object):
    _owner = ...

    def __init__(self, owner): ...

    def __call__(self, second: float, scope: float = None): ...


class BrowserWaiter(OriginWaiter):
    _owner: Chromium = ...

    def __init__(self, owner: Chromium): ...

    def __call__(self, second: float, scope: float = None) -> Chromium: ...

    def download_begin(self, timeout: float = None, cancel_it: bool = False) -> DownloadMission: ...

    def new_tab(self,
                timeout: float = None,
                curr_tab: Union[str, ChromiumTab] = None,
                raise_err: bool = None) -> Union[str, bool]: ...

    def all_downloads_done(self, timeout: float = None, cancel_if_timeout: bool = True) -> bool: ...


class BaseWaiter(OriginWaiter):
    _owner: ChromiumBase = ...

    def ele_deleted(self,
                    loc_or_ele: Union[str, tuple, ChromiumElement],
                    timeout: float = None,
                    raise_err: bool = None) -> bool: ...

    def ele_displayed(self,
                      loc_or_ele: Union[str, tuple, ChromiumElement],
                      timeout: float = None,
                      raise_err: bool = None) -> bool: ...

    def ele_hidden(self, loc_or_ele: Union[str, tuple, ChromiumElement], timeout: float = None,
                   raise_err: bool = None) -> bool: ...

    def eles_loaded(self,
                    locators: Union[Tuple[str, str], str, list, tuple],
                    timeout: float = None,
                    any_one: bool = False,
                    raise_err: bool = None) -> bool: ...

    def _loading(self, timeout: float = None, start: bool = True, gap: float = .01, raise_err: bool = None) -> bool: ...

    def load_start(self, timeout: float = None, raise_err: bool = None) -> bool: ...

    def doc_loaded(self, timeout: float = None, raise_err: bool = None) -> bool: ...

    def upload_paths_inputted(self) -> bool: ...

    def download_begin(self, timeout: float = None, cancel_it: bool = False) -> Union[DownloadMission, bool]: ...

    def downloads_done(self, timeout: float = None, cancel_if_timeout: bool = True) -> bool: ...

    def url_change(self, text: str, exclude: bool = False, timeout: float = None, raise_err: bool = None) -> bool: ...

    def title_change(self, text: str, exclude: bool = False, timeout: float = None, raise_err: bool = None) -> bool: ...

    def _change(self, arg: str, text: str, exclude: bool = False, timeout: float = None,
                raise_err: bool = None) -> bool: ...


class TabWaiter(BaseWaiter):
    _owner: Union[ChromiumTab, MixTab] = ...

    def __init__(self, owner: Union[ChromiumTab, MixTab]): ...

    def __call__(self, second: float, scope: float = None) -> Union[ChromiumTab, MixTab]: ...

    def downloads_done(self, timeout: float = None, cancel_if_timeout: bool = True) -> bool: ...

    def alert_closed(self) -> None: ...

    def url_change(self, text: str, exclude: bool = False,
                   timeout: float = None, raise_err: bool = None) -> Union[False, ChromiumTab, MixTab]: ...

    def title_change(self, text: str, exclude: bool = False,
                     timeout: float = None, raise_err: bool = None) -> Union[False, ChromiumTab, MixTab]: ...


class PageWaiter(TabWaiter):
    _owner: Union[ChromiumPage, MixPage] = ...

    def __init__(self, owner: Union[ChromiumPage, MixPage]): ...

    def __call__(self, second: float, scope: float = None) -> Union[ChromiumPage, MixPage]: ...

    def new_tab(self, timeout: float = None, raise_err: bool = None) -> Union[str, bool]: ...

    def all_downloads_done(self, timeout: float = None, cancel_if_timeout: bool = True) -> bool: ...

    def url_change(self, text: str, exclude: bool = False,
                   timeout: float = None, raise_err: bool = None) -> Union[False, ChromiumPage, MixPage]: ...

    def title_change(self, text: str, exclude: bool = False,
                     timeout: float = None, raise_err: bool = None) -> Union[False, ChromiumPage, MixPage]: ...


class ElementWaiter(OriginWaiter):
    _owner: ChromiumElement = ...
    _ele: ChromiumElement = ...

    def __init__(self, owner: ChromiumElement): ...

    def __call__(self, second: float, scope: float = None) -> ChromiumElement: ...

    @property
    def _timeout(self) -> float: ...

    def deleted(self, timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def displayed(self, timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def hidden(self, timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def covered(self, timeout: float = None, raise_err: bool = None) -> Union[False, int]: ...

    def not_covered(self, timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def enabled(self, timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def disabled(self, timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def clickable(self, wait_moved: bool = True,
                  timeout: float = None, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def has_rect(self,
                 timeout: float = None,
                 raise_err: bool = None) -> Union[False, List[Tuple[float, float]]]: ...

    def disabled_or_deleted(self, timeout: float = None, raise_err: bool = None) -> bool: ...

    def stop_moving(self, timeout: float = None, gap: float = .1, raise_err: bool = None) -> Union[ChromiumElement, False]: ...

    def _wait_state(self,
                    attr: str,
                    mode: bool = False,
                    timeout: float = None,
                    raise_err: bool = None,
                    err_text: str = None) -> Union[ChromiumElement, False]: ...


class FrameWaiter(BaseWaiter, ElementWaiter):
    _owner: ChromiumFrame = ...

    def __init__(self, owner: ChromiumFrame): ...

    def __call__(self, second: float, scope: float = None) -> ChromiumFrame: ...

    def url_change(self, text: str, exclude: bool = False,
                   timeout: float = None, raise_err: bool = None) -> Union[False, ChromiumFrame]: ...

    def title_change(self, text: str, exclude: bool = False,
                     timeout: float = None, raise_err: bool = None) -> Union[False, ChromiumFrame]: ...
