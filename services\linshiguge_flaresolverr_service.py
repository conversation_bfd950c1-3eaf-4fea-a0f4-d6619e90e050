"""
使用FlareSolverr的临时谷歌邮箱服务
"""
import asyncio
import re
from typing import Optional, Dict, Any
from datetime import datetime
from bs4 import BeautifulSoup

from services.email_service_base import EmailServiceBase
from core.models import EmailInfo
from core.flaresolverr_client import FlareSolverrClient
from utils.logger import app_logger
from config.settings import settings


class LinshigugeFlareSolverrService(EmailServiceBase):
    """使用FlareSolverr的临时谷歌邮箱服务"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("linshiguge_flaresolverr", config)
        self.base_url = "https://www.linshiguge.com/"
        self.flaresolverr_client: Optional[FlareSolverrClient] = None
    
    async def create_email(self) -> Optional[EmailInfo]:
        """创建临时邮箱"""
        try:
            app_logger.info(f"开始创建{self.name}邮箱")
            
            # 创建FlareSolverr客户端
            async with FlareSolverrClient(settings.FLARESOLVERR_URL) as client:
                # 检查FlareSolverr服务状态
                if not await client.check_health():
                    app_logger.error("FlareSolverr服务不可用")
                    return None
                
                # 解决Cloudflare挑战并获取页面内容
                solution = await client.solve_challenge(
                    self.base_url, 
                    max_timeout=settings.FLARESOLVERR_TIMEOUT
                )
                
                if not solution:
                    app_logger.error("FlareSolverr解决Cloudflare挑战失败")
                    return None
                
                html_content = solution.get("html", "")
                if not html_content:
                    app_logger.error("未获取到页面内容")
                    return None
                
                # 解析HTML内容
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 查找邮箱地址
                email_address = await self._extract_email_from_html(soup)
                
                if not email_address:
                    app_logger.error("无法从页面提取邮箱地址")
                    return None
                
                # 验证邮箱格式
                if not self._validate_email(email_address):
                    app_logger.error(f"邮箱格式无效: {email_address}")
                    return None
                
                email_info = EmailInfo(
                    email=email_address,
                    service=self.name,
                    created_at=datetime.now()
                )
                
                app_logger.info(f"成功创建邮箱: {email_address}")
                return email_info
                
        except Exception as e:
            app_logger.error(f"创建邮箱失败: {e}")
            return None
    
    async def get_verification_code(self, email_info: EmailInfo, timeout: int = 60) -> Optional[str]:
        """获取验证码"""
        try:
            app_logger.info(f"开始获取验证码: {email_info.email}")
            
            async with FlareSolverrClient(settings.FLARESOLVERR_URL) as client:
                # 检查服务状态
                if not await client.check_health():
                    app_logger.error("FlareSolverr服务不可用")
                    return None
                
                # 等待邮件到达
                verification_code = await self._wait_for_verification_email(
                    client, email_info.email, timeout
                )
                
                if verification_code:
                    email_info.verification_code = verification_code
                    email_info.code_received_at = datetime.now()
                    app_logger.info(f"成功获取验证码: {verification_code}")
                else:
                    app_logger.error("获取验证码超时")
                
                return verification_code
                
        except Exception as e:
            app_logger.error(f"获取验证码失败: {e}")
            return None
    
    async def _extract_email_from_html(self, soup: BeautifulSoup) -> Optional[str]:
        """从HTML中提取邮箱地址"""
        try:
            # 方法1: 查找输入框
            input_selectors = [
                'input[placeholder*="点击复制该邮箱"]',
                'input[placeholder*="复制"]',
                'input[type="text"]'
            ]
            
            for selector in input_selectors:
                elements = soup.select(selector)
                for element in elements:
                    value = element.get('value', '')
                    if value and '@gmail.com' in value:
                        app_logger.info(f"从输入框提取邮箱: {value}")
                        return value.strip()
            
            # 方法2: 从页面文本中提取
            page_text = soup.get_text()
            email_pattern = r'([a-zA-Z0-9._%+-]+@gmail\.com)'
            matches = re.findall(email_pattern, page_text)
            
            if matches:
                email_address = matches[0]
                app_logger.info(f"从页面文本提取邮箱: {email_address}")
                return email_address
            
            # 方法3: 查找特定的文本节点
            text_nodes = soup.find_all(text=re.compile(r'[a-zA-Z0-9._%+-]+@gmail\.com'))
            for node in text_nodes:
                matches = re.findall(email_pattern, str(node))
                if matches:
                    email_address = matches[0]
                    app_logger.info(f"从文本节点提取邮箱: {email_address}")
                    return email_address
            
            return None
            
        except Exception as e:
            app_logger.error(f"从HTML提取邮箱失败: {e}")
            return None
    
    async def _wait_for_verification_email(self, client: FlareSolverrClient, 
                                         email: str, timeout: int) -> Optional[str]:
        """等待验证邮件"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                # 刷新页面获取最新邮件
                solution = await client.solve_challenge(self.base_url)
                
                if not solution:
                    app_logger.warning("刷新页面失败")
                    await asyncio.sleep(5)
                    continue
                
                html_content = solution.get("html", "")
                if not html_content:
                    await asyncio.sleep(5)
                    continue
                
                # 解析HTML查找邮件
                soup = BeautifulSoup(html_content, 'html.parser')
                verification_code = await self._extract_verification_code_from_html(soup)
                
                if verification_code:
                    return verification_code
                
                # 等待一段时间后重试
                await asyncio.sleep(10)
            
            return None
            
        except Exception as e:
            app_logger.error(f"等待验证邮件失败: {e}")
            return None
    
    async def _extract_verification_code_from_html(self, soup: BeautifulSoup) -> Optional[str]:
        """从HTML中提取验证码"""
        try:
            # 查找邮件表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    
                    # 检查是否是验证邮件
                    row_text = row.get_text().lower()
                    if any(keyword in row_text for keyword in [
                        '验证码', 'verification', 'code', 'verify', 'confirm'
                    ]):
                        # 尝试从行文本中提取验证码
                        full_text = row.get_text()
                        code = self._extract_code_from_text(full_text)
                        if code:
                            return code
            
            # 如果表格中没找到，从整个页面文本中查找
            page_text = soup.get_text()
            return self._extract_code_from_text(page_text)
            
        except Exception as e:
            app_logger.error(f"从HTML提取验证码失败: {e}")
            return None
    
    def _extract_code_from_text(self, text: str) -> Optional[str]:
        """从文本中提取验证码"""
        try:
            # 验证码模式
            code_patterns = [
                r'验证码[：:\s]*([A-Za-z0-9]{4,8})',
                r'verification code[：:\s]*([A-Za-z0-9]{4,8})',
                r'code[：:\s]*([A-Za-z0-9]{4,8})',
                r'([A-Za-z0-9]{6})',  # 6位数字或字母
                r'([0-9]{4,8})',      # 4-8位数字
            ]
            
            for pattern in code_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    return matches[0]
            
            return None
            
        except Exception as e:
            app_logger.error(f"从文本提取验证码失败: {e}")
            return None
    
    def _validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
