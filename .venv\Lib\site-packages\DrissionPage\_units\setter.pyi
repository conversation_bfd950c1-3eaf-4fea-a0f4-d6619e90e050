# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from pathlib import Path
from typing import Union, Tuple, Literal, Any, Optional

from requests.adapters import HTTPAdapter
from requests.auth import HTTPBasicAuth

from .cookies_setter import <PERSON><PERSON>ookiesSetter, CookiesSetter, MixPageCookiesSetter, BrowserCookiesSetter
from .scroller import PageScroller
from .._base.base import BasePage
from .._base.browser import Chromium
from .._elements.chromium_element import ChromiumElement
from .._pages.chromium_base import ChromiumBase
from .._pages.chromium_frame import ChromiumFrame
from .._pages.chromium_page import ChromiumPage
from .._pages.tabs import ChromiumTab, MixTab
from .._pages.session_page import SessionPage
from .._pages.mix_page import MixPage

FILE_EXISTS = Literal['skip', 'rename', 'overwrite', 's', 'r', 'o']


class BaseSetter(object):
    def __init__(self, owner: Union[Chromium, BasePage]):
        self._owner: Union[Chromium, BasePage] = ...

    def NoneElement_value(self, value: Any = None, on_off: bool = True) -> None: ...

    def retry_times(self, times: int) -> None: ...

    def retry_interval(self, interval: float) -> None: ...

    def download_path(self, path: Union[str, Path, None]) -> None: ...


class SessionPageSetter(BaseSetter):
    _owner: SessionPage = ...
    _cookies_setter: Optional[SessionCookiesSetter] = ...

    def __init__(self, owner: SessionPage): ...

    @property
    def cookies(self) -> SessionCookiesSetter: ...

    def retry_times(self, times: int) -> None: ...

    def retry_interval(self, interval: float) -> None: ...

    def download_path(self, path: Union[str, Path, None]) -> None: ...

    def timeout(self, second: float) -> None: ...

    def encoding(self, encoding: Union[str, None], set_all: bool = True) -> None: ...

    def headers(self, headers: Union[str, dict]) -> None: ...

    def header(self, name: str, value: str) -> None: ...

    def user_agent(self, ua: str) -> None: ...

    def proxies(self, http: str = None, https: str = None) -> None: ...

    def auth(self, auth: Union[Tuple[str, str], HTTPBasicAuth, None]) -> None: ...

    def hooks(self, hooks: Union[dict, None]) -> None: ...

    def params(self, params: Union[dict, None]) -> None: ...

    def verify(self, on_off: Union[bool, None]) -> None: ...

    def cert(self, cert: Union[str, Tuple[str, str], None]) -> None: ...

    def stream(self, on_off: Union[bool, None]) -> None: ...

    def trust_env(self, on_off: Union[bool, None]) -> None: ...

    def max_redirects(self, times: Union[int, None]) -> None: ...

    def add_adapter(self, url: str, adapter: HTTPAdapter) -> None: ...


class BrowserBaseSetter(BaseSetter):
    _cookies_setter: Optional[CookiesSetter] = ...

    @property
    def load_mode(self) -> LoadMode: ...

    def timeouts(self, base=None, page_load=None, script=None) -> None: ...


class BrowserSetter(BrowserBaseSetter):
    _owner: Chromium = ...
    _cookies_setter: BrowserCookiesSetter = ...

    @property
    def cookies(self) -> BrowserCookiesSetter: ...

    def auto_handle_alert(self, on_off: bool = True, accept: bool = True): ...

    def download_path(self, path: Union[Path, str, None]): ...

    def download_file_name(self, name: str = None, suffix: str = None): ...

    def when_download_file_exists(self, mode: FILE_EXISTS): ...


class ChromiumBaseSetter(BrowserBaseSetter):
    _owner: ChromiumBase = ...
    _cookies_setter: CookiesSetter = ...

    def __init__(self, owner): ...

    @property
    def scroll(self) -> PageScrollSetter: ...

    @property
    def cookies(self) -> CookiesSetter: ...

    def user_agent(self, ua: str, platform: str = None) -> None: ...

    def session_storage(self, item: str, value: Union[str, bool]) -> None: ...

    def local_storage(self, item: str, value: Union[str, bool]) -> None: ...

    def headers(self, headers: Union[dict, str]) -> None: ...

    def auto_handle_alert(self, on_off: bool = True, accept: bool = True) -> None: ...

    def upload_files(self, files: Union[str, Path, list, tuple]) -> None: ...

    def blocked_urls(self, urls: Union[list, tuple, str, None]) -> None: ...


class TabSetter(ChromiumBaseSetter):
    _owner: ChromiumTab = ...

    def __init__(self, owner: Union[ChromiumTab, MixTab, MixPage, ChromiumPage]): ...

    @property
    def window(self) -> WindowSetter: ...

    def download_path(self, path: Union[str, Path, None]) -> None: ...

    def download_file_name(self, name: str = None, suffix: str = None) -> None: ...

    def when_download_file_exists(self, mode: FILE_EXISTS) -> None: ...

    def activate(self) -> None: ...


class ChromiumPageSetter(TabSetter):
    _owner: ChromiumPage = ...

    def auto_handle_alert(self, on_off: bool = True, accept: bool = True, all_tabs: bool = False) -> None: ...


class MixPageSetter(ChromiumPageSetter):
    _owner: MixPage = ...
    _session_setter: SessionPageSetter = ...
    _chromium_setter: ChromiumPageSetter = ...

    def user_agent(self, ua: str, platform: str = None) -> None: ...

    def headers(self, headers: Union[str, dict]) -> None: ...

    @property
    def cookies(self) -> MixPageCookiesSetter: ...


class MixTabSetter(TabSetter):
    _owner: MixTab = ...
    _session_setter: SessionPageSetter = ...
    _chromium_setter: ChromiumBaseSetter = ...

    def user_agent(self, ua: str, platform: str = None) -> None: ...

    def headers(self, headers: Union[str, dict]) -> None: ...

    @property
    def cookies(self) -> MixPageCookiesSetter: ...

    def timeouts(self, base: float = None, page_load: float = None, script: float = None) -> None: ...


class ChromiumElementSetter(object):
    def __init__(self, ele: ChromiumElement):
        self._ele: ChromiumElement = ...

    def attr(self, name: str, value: str = '') -> None: ...

    def property(self, name: str, value: str) -> None: ...

    def style(self, name: str, value: str) -> None: ...

    def innerHTML(self, html: str) -> None: ...

    def value(self, value: str) -> None: ...


class ChromiumFrameSetter(ChromiumBaseSetter):
    _owner: ChromiumFrame = ...

    def attr(self, name: str, value: str) -> None: ...


class LoadMode(object):
    _owner: Union[Chromium, ChromiumBase] = ...

    def __init__(self, owner: Union[Chromium, ChromiumBase]): ...

    def __call__(self, value: str) -> None: ...

    def normal(self) -> None: ...

    def eager(self) -> None: ...

    def none(self) -> None: ...


class PageScrollSetter(object):
    def __init__(self, scroll: PageScroller):
        self._scroll: PageScroller = ...

    def wait_complete(self, on_off: bool = True): ...

    def smooth(self, on_off: bool = True): ...


class WindowSetter(object):
    def __init__(self, owner: ChromiumBase):
        self._owner: ChromiumBase = ...
        self._window_id: str = ...

    def max(self) -> None: ...

    def mini(self) -> None: ...

    def full(self) -> None: ...

    def normal(self) -> None: ...

    def size(self, width: int = None, height: int = None) -> None: ...

    def location(self, x: int = None, y: int = None) -> None: ...

    def _get_info(self) -> dict: ...

    def _perform(self, bounds: dict) -> None: ...

    def hide(self) -> None: ...

    def show(self) -> None: ...
