"""
FlareSolverr客户端 - 用于绕过Cloudflare保护
"""
import asyncio
import json
from typing import Optional, Dict, Any
import httpx
from utils.logger import app_logger


class FlareSolverrClient:
    """FlareSolverr客户端"""
    
    def __init__(self, flaresolverr_url: str = "http://localhost:8191"):
        self.flaresolverr_url = flaresolverr_url
        self.session_id: Optional[str] = None
        self.client: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = httpx.AsyncClient(timeout=120.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session_id:
            await self.destroy_session()
        if self.client:
            await self.client.aclose()
    
    async def create_session(self) -> bool:
        """创建FlareSolverr会话"""
        try:
            payload = {
                "cmd": "sessions.create",
                "session": f"session_{asyncio.get_event_loop().time()}"
            }
            
            response = await self.client.post(
                f"{self.flaresolverr_url}/v1",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "ok":
                    self.session_id = result.get("session")
                    app_logger.info(f"FlareSolverr会话创建成功: {self.session_id}")
                    return True
            
            app_logger.error(f"创建FlareSolverr会话失败: {response.text}")
            return False
            
        except Exception as e:
            app_logger.error(f"创建FlareSolverr会话异常: {e}")
            return False
    
    async def destroy_session(self) -> bool:
        """销毁FlareSolverr会话"""
        if not self.session_id:
            return True
        
        try:
            payload = {
                "cmd": "sessions.destroy",
                "session": self.session_id
            }
            
            response = await self.client.post(
                f"{self.flaresolverr_url}/v1",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "ok":
                    app_logger.info(f"FlareSolverr会话销毁成功: {self.session_id}")
                    self.session_id = None
                    return True
            
            app_logger.error(f"销毁FlareSolverr会话失败: {response.text}")
            return False
            
        except Exception as e:
            app_logger.error(f"销毁FlareSolverr会话异常: {e}")
            return False
    
    async def solve_challenge(self, url: str, max_timeout: int = 60000) -> Optional[Dict[str, Any]]:
        """
        解决Cloudflare挑战
        
        Args:
            url: 目标URL
            max_timeout: 最大超时时间(毫秒)
            
        Returns:
            Dict: 包含页面内容和cookies的字典，失败返回None
        """
        try:
            # 如果没有会话，先创建一个
            if not self.session_id:
                if not await self.create_session():
                    return None
            
            app_logger.info(f"使用FlareSolverr解决Cloudflare挑战: {url}")
            
            payload = {
                "cmd": "request.get",
                "url": url,
                "session": self.session_id,
                "maxTimeout": max_timeout
            }
            
            response = await self.client.post(
                f"{self.flaresolverr_url}/v1",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("status") == "ok":
                    solution = result.get("solution", {})
                    app_logger.info("FlareSolverr成功解决Cloudflare挑战")
                    
                    return {
                        "url": solution.get("url"),
                        "status": solution.get("status"),
                        "headers": solution.get("headers", {}),
                        "cookies": solution.get("cookies", []),
                        "userAgent": solution.get("userAgent"),
                        "html": solution.get("response")
                    }
                else:
                    error_msg = result.get("message", "未知错误")
                    app_logger.error(f"FlareSolverr解决挑战失败: {error_msg}")
                    return None
            else:
                app_logger.error(f"FlareSolverr请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            app_logger.error(f"FlareSolverr解决挑战异常: {e}")
            return None
    
    async def post_request(self, url: str, post_data: Dict[str, Any], 
                          max_timeout: int = 60000) -> Optional[Dict[str, Any]]:
        """
        发送POST请求（通过FlareSolverr）
        
        Args:
            url: 目标URL
            post_data: POST数据
            max_timeout: 最大超时时间(毫秒)
            
        Returns:
            Dict: 响应数据，失败返回None
        """
        try:
            if not self.session_id:
                if not await self.create_session():
                    return None
            
            app_logger.info(f"使用FlareSolverr发送POST请求: {url}")
            
            payload = {
                "cmd": "request.post",
                "url": url,
                "session": self.session_id,
                "postData": json.dumps(post_data),
                "maxTimeout": max_timeout
            }
            
            response = await self.client.post(
                f"{self.flaresolverr_url}/v1",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("status") == "ok":
                    solution = result.get("solution", {})
                    app_logger.info("FlareSolverr POST请求成功")
                    
                    return {
                        "url": solution.get("url"),
                        "status": solution.get("status"),
                        "headers": solution.get("headers", {}),
                        "cookies": solution.get("cookies", []),
                        "userAgent": solution.get("userAgent"),
                        "html": solution.get("response")
                    }
                else:
                    error_msg = result.get("message", "未知错误")
                    app_logger.error(f"FlareSolverr POST请求失败: {error_msg}")
                    return None
            else:
                app_logger.error(f"FlareSolverr POST请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            app_logger.error(f"FlareSolverr POST请求异常: {e}")
            return None
    
    async def check_health(self) -> bool:
        """检查FlareSolverr服务健康状态"""
        try:
            response = await self.client.get(f"{self.flaresolverr_url}/health")
            
            if response.status_code == 200:
                app_logger.info("FlareSolverr服务运行正常")
                return True
            else:
                app_logger.error(f"FlareSolverr服务异常: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            app_logger.error(f"检查FlareSolverr服务失败: {e}")
            return False
